import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { PageContainer, Stack } from '../../components/ui/layout';
import SignupForm from '../../components/auth/SignupForm';
import teacherImage from '../../assets/images/auth/teacher-signup-image.png';
import mentorImage from '../../assets/images/auth/mentor-signup-image.png';
import institutionImage from '../../assets/images/auth/institution-signup-image.png';
import sponsorImage from '../../assets/images/auth/sponsor-signup-image.png';

const Signup = () => {
  const [searchParams] = useSearchParams();
  const role = searchParams.get('role') || 'student';

  const showIllustration = ['institution', 'sponsor', 'mentor', 'teacher'].includes(role);

  const getIllustration = () => {
    switch (role) {
      case 'teacher':
        return teacherImage;
      case 'mentor':
        return mentorImage;
      case 'institution':
        return institutionImage;
      case 'sponsor':
        return sponsorImage;
      default:
        return teacherImage;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-violet-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800 flex items-center justify-center px-4 py-8">
      <PageContainer maxWidth="7xl">
        <div className={`grid gap-8 lg:gap-12 items-center ${showIllustration ? 'grid-cols-1 lg:grid-cols-2' : 'grid-cols-1 max-w-lg mx-auto'}`}>
          {showIllustration && (
            <div className="hidden lg:flex justify-center items-center order-2 lg:order-1">
              <div className="relative">
                <img
                  src={getIllustration()}
                  alt={`${role} signup illustration`}
                  className="w-full max-w-lg h-auto object-contain drop-shadow-2xl"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-violet-100/20 to-transparent rounded-3xl"></div>
              </div>
            </div>
          )}

          <div className={`w-full ${showIllustration ? 'order-1 lg:order-2' : ''}`}>
            <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl border border-gray-100 dark:border-gray-700 p-8 lg:p-10">
              <SignupForm role={role} />
            </div>
          </div>
        </div>
      </PageContainer>
    </div>
  );
};

export default Signup;
