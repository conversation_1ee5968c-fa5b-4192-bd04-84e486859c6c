import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { PageContainer, Stack } from '../../components/ui/layout';
import SignupForm from '../../components/auth/SignupForm';
import teacherImage from '../../assets/images/auth/teacher-signup-image.png';
import mentorImage from '../../assets/images/auth/mentor-signup-image.png';
import institutionImage from '../../assets/images/auth/institution-signup-image.png';
import sponsorImage from '../../assets/images/auth/sponsor-signup-image.png';

const Signup = () => {
  const [searchParams] = useSearchParams();
  const role = searchParams.get('role') || 'student';

  const showIllustration = ['institution', 'sponsor', 'mentor', 'teacher'].includes(role);

  const getIllustration = () => {
    switch (role) {
      case 'teacher':
        return teacherImage;
      case 'mentor':
        return mentorImage;
      case 'institution':
        return institutionImage;
      case 'sponsor':
        return sponsorImage;
      default:
        return teacherImage;
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center px-4">
      <PageContainer maxWidth="6xl">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          {showIllustration && (
            <div className="hidden md:block">
              <img
                src={getIllustration()}
                alt={`${role} signup illustration`}
                className="w-full max-w-md mx-auto"
              />
            </div>
          )}

          <div className={showIllustration ? '' : 'md:col-span-2 max-w-md mx-auto'}>
            <SignupForm role={role} />
          </div>
        </div>
      </PageContainer>
    </div>
  );
};

export default Signup;
