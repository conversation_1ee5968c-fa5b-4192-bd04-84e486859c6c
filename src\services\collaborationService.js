import axios from 'axios';
import API_BASE_URL from '../utils/api/API_URL';

/**
 * Collaboration Service for Institute-Mentor Invitations
 * Handles invitations between institutes and mentors
 */

const BASE_URL = `${API_BASE_URL}/api/institute/mentors`;
const getAuthToken = () => localStorage.getItem("token");

// Get sent invitations (for both institutes and mentors)
export const getSentInvitations = async (page = 1, size = 20) => {
  try {
    const response = await axios.get(`${BASE_URL}/invites/sent`, {
      params: { page, size },
      headers: { Authorization: `Bearer ${getAuthToken()}` }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching sent invitations:', error);
    throw error;
  }
};

// Get received invitations (for both institutes and mentors)
export const getReceivedInvitations = async (page = 1, size = 20) => {
  try {
    const response = await axios.get(`${BASE_URL}/invites/received`, {
      params: { page, size },
      headers: { Authorization: `Bearer ${getAuthToken()}` }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching received invitations:', error);
    throw error;
  }
};

// Send invitation (institute to mentor or mentor to institute)
export const sendInvitation = async (invitationData) => {
  try {
    const response = await axios.post(`${BASE_URL}/invite`, invitationData, {
      headers: { Authorization: `Bearer ${getAuthToken()}` }
    });
    return response.data;
  } catch (error) {
    console.error('Error sending invitation:', error);
    throw error;
  }
};

// Respond to invitation (accept/reject)
export const respondToInvitation = async (invitationId, response = {}) => {
  try {
    const result = await axios.post(`${BASE_URL}/invite/${invitationId}/respond`, response, {
      headers: { Authorization: `Bearer ${getAuthToken()}` }
    });
    return result.data;
  } catch (error) {
    console.error('Error responding to invitation:', error);
    throw error;
  }
};

// Helper function to create invitation payload
export const createInvitationPayload = (receiverId, hourlyRate, hoursPerWeek, receivedBy) => ({
  receiver_id: receiverId,
  hourly_rate: hourlyRate || 0,
  hours_per_week: hoursPerWeek || 0,
  received_by: receivedBy // "mentor" or "institute"
});

// Get all invitations (both sent and received)
export const getAllInvitations = async (page = 1, size = 20) => {
  try {
    const [sentData, receivedData] = await Promise.all([
      getSentInvitations(page, size),
      getReceivedInvitations(page, size)
    ]);
    
    return {
      sent: sentData,
      received: receivedData
    };
  } catch (error) {
    console.error('Error fetching all invitations:', error);
    throw error;
  }
};

export default {
  getSentInvitations,
  getReceivedInvitations,
  sendInvitation,
  respondToInvitation,
  createInvitationPayload,
  getAllInvitations
};
