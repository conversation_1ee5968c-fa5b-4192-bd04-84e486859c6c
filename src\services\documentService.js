/**
 * Document Service
 * Service for fetching institute documents using the new API endpoint
 */

import axios from 'axios';
import { getAuthToken } from '../utils/helpers/authHelpers';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://edufair.duckdns.org';

/**
 * Fetch document binary data by document path
 * @param {string} documentPath - The relative path to the document
 * @returns {Promise<Object>} Document data with base64, mime type, etc.
 */
export const fetchDocument = async (documentPath) => {
  try {
    console.log('Fetching document:', documentPath);
    console.log('API URL:', `${API_BASE_URL}/api/institutes/document/${documentPath}`);

    const response = await axios.get(`${API_BASE_URL}/api/institutes/document/${documentPath}`, {
      headers: {
        'Authorization': `Bearer ${getAuthToken()}`
      }
    });

    console.log('Document fetch response:', response.data);

    if (response.status === 200) {
      return response.data;
    } else {
      throw new Error(`Failed to fetch document: ${response.status}`);
    }
  } catch (error) {
    console.error('Error fetching document:', error);
    console.error('Error details:', error.response?.data);
    throw error;
  }
};

/**
 * Download document using the fetch API
 * @param {string} documentPath - The relative path to the document
 * @param {string} filename - Optional custom filename for download
 */
export const downloadDocument = async (documentPath, filename = null) => {
  try {
    const data = await fetchDocument(documentPath);
    
    // Use the data URL for immediate download
    const link = document.createElement('a');
    link.href = data.data_url;
    link.download = filename || getFilenameFromPath(data.document_path);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    return data;
  } catch (error) {
    console.error('Error downloading document:', error);
    throw error;
  }
};

/**
 * Display document in browser (for PDFs) or trigger download
 * @param {string} documentPath - The relative path to the document
 * @param {HTMLElement} container - Optional container element for display
 */
export const displayDocument = async (documentPath, container = null) => {
  try {
    const data = await fetchDocument(documentPath);
    
    // Create blob from base64
    const byteCharacters = atob(data.base64_data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: data.mime_type });
    
    // Create object URL for display
    const objectUrl = URL.createObjectURL(blob);
    
    // Display in iframe (for PDFs) or download for other types
    if (data.mime_type === 'application/pdf') {
      if (container) {
        // Display in provided container
        const iframe = document.createElement('iframe');
        iframe.src = objectUrl;
        iframe.width = '100%';
        iframe.height = '600px';
        iframe.style.border = 'none';
        container.innerHTML = '';
        container.appendChild(iframe);
      } else {
        // Open in new tab
        window.open(objectUrl, '_blank');
      }
    } else {
      // For other files, trigger download
      await downloadDocument(documentPath);
    }
    
    return { data, objectUrl };
  } catch (error) {
    console.error('Error displaying document:', error);
    throw error;
  }
};

/**
 * Get document data for preview (without triggering download)
 * @param {string} documentPath - The relative path to the document
 * @returns {Promise<Object>} Document data with blob URL for preview
 */
export const getDocumentPreview = async (documentPath) => {
  try {
    const data = await fetchDocument(documentPath);

    console.log('Creating blob for document:', {
      path: documentPath,
      mimeType: data.mime_type,
      dataSize: data.base64_data.length
    });

    // Create blob from base64
    const byteCharacters = atob(data.base64_data);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: data.mime_type });

    // Create object URL
    const objectUrl = URL.createObjectURL(blob);

    // Also create data URL for fallback
    const dataUrl = `data:${data.mime_type};base64,${data.base64_data}`;

    console.log('Created blob URL:', objectUrl);
    console.log('Created data URL length:', dataUrl.length);
    console.log('Blob details:', {
      size: blob.size,
      type: blob.type
    });

    // For text files, extract text content
    let textContent = null;
    if (data.mime_type.startsWith('text/') ||
        data.mime_type === 'application/json' ||
        data.mime_type === 'application/xml') {
      try {
        textContent = new TextDecoder('utf-8').decode(byteArray);
      } catch (err) {
        console.warn('Failed to decode text content:', err);
      }
    }

    return {
      ...data,
      blob,
      objectUrl,
      data_url: dataUrl,
      textContent,
      filename: getFilenameFromPath(data.document_path)
    };
  } catch (error) {
    console.error('Error getting document preview:', error);
    throw error;
  }
};

/**
 * Check if document can be previewed in browser
 * @param {string} mimeType - The MIME type of the document
 * @returns {boolean} Whether the document can be previewed
 */
export const canPreviewDocument = (mimeType) => {
  const previewableMimeTypes = [
    // PDFs
    'application/pdf',

    // Images
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/bmp',
    'image/svg+xml',
    'image/tiff',
    'image/tif',

    // Text files
    'text/plain',
    'text/html',
    'text/css',
    'text/javascript',
    'text/csv',
    'application/json',
    'application/xml',
    'text/xml',
    'text/markdown',
    'text/x-markdown',
    'application/javascript',
    'application/typescript',
    'text/x-python',
    'text/x-java',
    'text/x-c',
    'text/x-cpp',
    'text/x-csharp',

    // Office documents (will use Google Docs Viewer or Office Online)
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.oasis.opendocument.text',
    'application/vnd.oasis.opendocument.spreadsheet',
    'application/vnd.oasis.opendocument.presentation',
    'application/rtf',

    // Additional formats
    'application/vnd.ms-word.document.macroEnabled.12',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    'application/vnd.openxmlformats-officedocument.presentationml.template',

    // Archive files (for preview info)
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/x-tar',
    'application/gzip',

    // Video files (basic support)
    'video/mp4',
    'video/webm',
    'video/ogg',
    'video/avi',
    'video/mov',
    'video/wmv',

    // Audio files
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/m4a',
    'audio/aac',
    'audio/flac'
  ];

  return previewableMimeTypes.includes(mimeType);
};

/**
 * Get document viewer type based on MIME type
 * @param {string} mimeType - The MIME type of the document
 * @returns {string} The viewer type: 'pdf', 'image', 'text', 'office', 'video', 'audio', 'archive', 'google-docs'
 */
export const getDocumentViewerType = (mimeType) => {
  if (mimeType === 'application/pdf') {
    return 'pdf';
  }

  if (mimeType.startsWith('image/')) {
    return 'image';
  }

  if (mimeType.startsWith('video/')) {
    return 'video';
  }

  if (mimeType.startsWith('audio/')) {
    return 'audio';
  }

  // Archive files
  const archiveTypes = [
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed',
    'application/x-tar',
    'application/gzip'
  ];

  if (archiveTypes.includes(mimeType)) {
    return 'archive';
  }

  // Text and code files
  if (mimeType.startsWith('text/') ||
      mimeType === 'application/json' ||
      mimeType === 'application/xml' ||
      mimeType === 'text/xml' ||
      mimeType === 'application/javascript' ||
      mimeType === 'application/typescript') {
    return 'text';
  }

  // Office documents that work well with Google Docs Viewer
  const officeTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'application/vnd.oasis.opendocument.text',
    'application/vnd.oasis.opendocument.spreadsheet',
    'application/vnd.oasis.opendocument.presentation',
    'application/rtf',
    'application/vnd.ms-word.document.macroEnabled.12',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
    'application/vnd.ms-powerpoint.presentation.macroEnabled.12',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.template',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.template',
    'application/vnd.openxmlformats-officedocument.presentationml.template'
  ];

  if (officeTypes.includes(mimeType)) {
    return 'google-docs';
  }

  return 'download'; // Fallback to download
};

/**
 * Get Google Docs Viewer URL for office documents
 * @param {string} documentUrl - The document URL or base64 data URL
 * @param {string} mimeType - The MIME type of the document
 * @returns {string} Google Docs Viewer URL
 */
export const getGoogleDocsViewerUrl = (documentUrl, mimeType) => {
  // For base64 data URLs, we need to create a temporary blob URL
  // Google Docs Viewer requires a publicly accessible URL
  return `https://docs.google.com/viewer?url=${encodeURIComponent(documentUrl)}&embedded=true`;
};

/**
 * Get Office Online Viewer URL (alternative to Google Docs)
 * @param {string} documentUrl - The document URL
 * @param {string} mimeType - The MIME type of the document
 * @returns {string} Office Online Viewer URL
 */
export const getOfficeOnlineViewerUrl = (documentUrl, mimeType) => {
  // Microsoft Office Online Viewer
  return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(documentUrl)}`;
};

/**
 * Get alternative viewer URLs for different document types
 * @param {string} documentUrl - The document URL
 * @param {string} mimeType - The MIME type of the document
 * @returns {Array} Array of viewer options
 */
export const getAlternativeViewers = (documentUrl, mimeType) => {
  const viewers = [];

  // Google Docs Viewer (works with most office documents)
  if (mimeType.includes('word') || mimeType.includes('excel') || mimeType.includes('powerpoint') ||
      mimeType.includes('opendocument') || mimeType === 'application/rtf') {
    viewers.push({
      name: 'Google Docs Viewer',
      url: getGoogleDocsViewerUrl(documentUrl, mimeType),
      type: 'iframe'
    });

    viewers.push({
      name: 'Office Online',
      url: getOfficeOnlineViewerUrl(documentUrl, mimeType),
      type: 'iframe'
    });
  }

  return viewers;
};

/**
 * Get filename from document path
 * @param {string} path - The document path
 * @returns {string} The filename
 */
export const getFilenameFromPath = (path) => {
  return path.split('/').pop();
};

/**
 * Get document type from path
 * @param {string} path - The document path
 * @returns {string} The document type
 */
export const getDocumentTypeFromPath = (path) => {
  const parts = path.split('/');
  if (parts.length >= 2 && parts[0] === 'institute_documents') {
    return parts[1]; // accreditation, license, certificate, other
  }
  return 'unknown';
};

/**
 * Format document size for display
 * @param {number} sizeBytes - Size in bytes
 * @returns {string} Formatted size string
 */
export const formatDocumentSize = (sizeBytes) => {
  if (sizeBytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(sizeBytes) / Math.log(k));
  
  return parseFloat((sizeBytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default {
  fetchDocument,
  downloadDocument,
  displayDocument,
  getDocumentPreview,
  canPreviewDocument,
  getDocumentViewerType,
  getGoogleDocsViewerUrl,
  getOfficeOnlineViewerUrl,
  getAlternativeViewers,
  getFilenameFromPath,
  getDocumentTypeFromPath,
  formatDocumentSize
};
