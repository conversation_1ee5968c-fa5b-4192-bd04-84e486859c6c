import React, { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  FiUser,
  FiSave,
  FiCamera,
  FiGlobe,
  FiDollarSign,
  FiClock,
  FiAward,
  FiFileText,
  FiPlus,
  FiX,
  FiCheck,
  FiAlertCircle,
  FiLoader
} from 'react-icons/fi';
import { useMentorProfile } from '../../hooks/useMentorProfile';
import { LoadingSpinner } from '../../components/ui';
import SubjectSelector from '../../components/mentor/SubjectSelector';
import { uploadProfilePicture } from '../../store/slices/userSlice';
import URL from '../../utils/api/API_URL';

const MentorSettings = () => {
  // Check authentication state
  const dispatch = useDispatch();
  const loginState = useSelector(state => state.login);
  const userSlice = useSelector(state => state.users);
  const userRole = localStorage.getItem('role');
  const token = localStorage.getItem('token');

  const {
    profile,
    isLoading,
    isSaving,
    saveError,
    validationErrors,
    saveSuccess,
    hasUnsavedChanges,
    updateLocalProfile,
    updateSubjectSelection,
    updateArrayField,
    addArrayItem,
    removeArrayItem,
    updateAvailability,
    saveProfile,
    resetChanges
  } = useMentorProfile();

  // UI state
  const [activeTab, setActiveTab] = useState('profile');
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [imageUploadSuccess, setImageUploadSuccess] = useState(false);
  const [imageError, setImageError] = useState(null);

  // Load profile image when profile changes
  useEffect(() => {
    if (profile) {
      console.log('Profile data in MentorSettings:', profile);
      console.log('Profile image URL:', profile?.profile_image_url);
      console.log('Profile picture:', profile?.profile_picture);

      // Don't set imagePreview here - let getCurrentProfileImage handle it
      // This prevents overriding the preview with the raw URL
    }
  }, [profile]);

  // Show success message when profile is updated
  useEffect(() => {
    if (saveSuccess) {
      setShowSuccessMessage(true);
      const timer = setTimeout(() => setShowSuccessMessage(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [saveSuccess]);

  // Refresh profile data when user slice is updated (after profile picture upload)
  useEffect(() => {
    if (userSlice.success && userSlice.success.includes('Profile picture')) {
      // The profile data should automatically update through the Redux store
      // Clear any preview since the actual image is now loaded
      setImagePreview(null);
    }
  }, [userSlice.success]);



  // Handle input changes
  const handleInputChange = useCallback((e) => {
    const { name, value, type, checked } = e.target;
    updateLocalProfile({
      [name]: type === 'checkbox' ? checked : value
    });
  }, [updateLocalProfile]);

  // Handle array field changes
  const handleArrayFieldChange = useCallback((fieldName, index, value) => {
    updateArrayField(fieldName, index, value);
  }, [updateArrayField]);

  // Add array field
  const addArrayField = useCallback((fieldName) => {
    addArrayItem(fieldName, '');
  }, [addArrayItem]);

  // Remove array field
  const removeArrayField = useCallback((fieldName, index) => {
    removeArrayItem(fieldName, index);
  }, [removeArrayItem]);

  // Handle availability changes
  const handleAvailabilityChange = useCallback((day, field, value) => {
    updateAvailability(day, field, value);
  }, [updateAvailability]);

  // Helper function to format file size
  const formatFileSize = useCallback((bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Helper function to get full image URL
  const getFullImageUrl = useCallback((imageUrl) => {
    if (!imageUrl) return null;

    // If it's base64 data, return as is
    if (imageUrl.startsWith('data:')) {
      return imageUrl;
    }

    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    // If it's a relative path, prepend the API base URL
    if (imageUrl.startsWith('/')) {
      return `${URL}${imageUrl}`;
    }

    // If it's just a filename, assume it's in the static folder
    return `${URL}/static/${imageUrl}`;
  }, []);

  // Get current profile image URL
  const getCurrentProfileImage = useCallback(() => {
    if (imagePreview) return imagePreview;

    // Priority: 1. Image data objects, 2. URL fields
    const imageData = profile?.profile_picture_data;
    const fullImageData = imageData?.full_image;
    const thumbnailData = imageData?.thumbnail;

    // Try different possible image URL fields
    const imageUrl = fullImageData?.data_url ||
                    thumbnailData?.data_url ||
                    profile?.profile_image_url ||
                    profile?.profile_picture ||
                    profile?.profile_picture_url;

    return getFullImageUrl(imageUrl);
  }, [imagePreview, profile, getFullImageUrl]);



  // Handle image upload - immediately upload to server
  const handleImageUpload = useCallback(async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Clear previous errors
    setImageError(null);
    setImageUploadSuccess(false);

    console.log(`Selected file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      setImageError('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      e.target.value = ''; // Clear the input
      return;
    }

    // Check file size - backend allows 20MB
    const maxFileSize = 20 * 1024 * 1024; // 20MB limit to match backend
    if (file.size > maxFileSize) {
      const fileSize = formatFileSize(file.size);
      const maxSize = formatFileSize(maxFileSize);

      setImageError(
        `Image size too large (${fileSize}). Maximum allowed is ${maxSize}. Please choose a smaller image file.`
      );

      e.target.value = ''; // Clear the input
      return;
    }

    try {
      setIsUploadingImage(true);

      // Create preview immediately
      const reader = new FileReader();
      reader.onload = (e) => setImagePreview(e.target.result);
      reader.readAsDataURL(file);

      // Upload to server immediately
      const result = await dispatch(uploadProfilePicture(file)).unwrap();

      // Show success message and clear any errors
      setImageUploadSuccess(true);
      setImageError(null);
      setTimeout(() => setImageUploadSuccess(false), 3000);

      // Clear the file input
      e.target.value = '';

      console.log('Profile picture uploaded successfully:', result);

    } catch (error) {
      console.error('Failed to upload profile picture:', error);

      // Show specific error messages
      if (error.message && (error.message.includes('413') || error.message.includes('too large'))) {
        setImageError('File is too large for the server. Please reduce your image size to under 20MB.');
      } else if (error.message && error.message.includes('network')) {
        setImageError('Network error. Please check your connection and try again.');
      } else {
        setImageError('Failed to upload profile picture. Please try again with a different image.');
      }

      setImagePreview(null);
      e.target.value = ''; // Clear the input
    } finally {
      setIsUploadingImage(false);
    }
  }, [dispatch]);

  // Submit form - profile image is handled separately now
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    const result = await saveProfile(); // No need to pass profileImage anymore
    if (result.success) {
      // Profile saved successfully
    }
  }, [saveProfile]);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: FiUser },
    { id: 'expertise', label: 'Expertise', icon: FiAward },
    { id: 'availability', label: 'Availability', icon: FiClock },
    { id: 'preferences', label: 'Preferences', icon: FiFileText }
  ];

  // Check if user needs to register as mentor first
  if (!profile && !isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-start">
            <FiAlertCircle className="h-5 w-5 text-yellow-500 mt-0.5 mr-3" />
            <div className="flex-1">
              <h3 className="text-sm font-medium text-yellow-800">Mentor Registration Required</h3>
              <p className="mt-1 text-sm text-yellow-700">
                You need to register as a mentor first before you can access the settings page.
              </p>
              <div className="mt-3">
                <button
                  onClick={() => window.location.href = '/mentors/register'}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-yellow-800 bg-yellow-100 hover:bg-yellow-200 transition-colors"
                >
                  Register as Mentor
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Profile Settings</h1>
            <p className="mt-2 text-gray-600">
              Manage your mentor profile and preferences
            </p>
          </div>
          
          {/* Save Button */}
          <button
            onClick={handleSubmit}
            disabled={!hasUnsavedChanges || isSaving}
            className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md transition-colors ${
              hasUnsavedChanges && !isSaving
                ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'
                : 'text-gray-400 bg-gray-100 cursor-not-allowed'
            }`}
            title={!hasUnsavedChanges ? 'No changes to save' : isSaving ? 'Saving...' : 'Save changes'}
          >
            {isSaving ? (
              <LoadingSpinner size="sm" className="mr-2" />
            ) : (
              <FiSave className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </button>


        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex items-center">
              <FiCheck className="h-5 w-5 text-green-500 mr-2" />
              <span className="text-green-800">Profile updated successfully!</span>
            </div>
          </div>
        )}

        {/* Error Message */}
        {saveError && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <FiAlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-800">
                {typeof saveError === 'string'
                  ? saveError
                  : saveError.detail || saveError.message || 'Failed to save profile'
                }
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center">
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="space-y-6">
            {/* Profile Image */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Profile Image</h3>
              <div className="flex items-center space-x-6">
                <div className="relative">
                  <div className="w-24 h-24 rounded-full overflow-hidden bg-gray-100 border-2 border-gray-200">
                    {getCurrentProfileImage() ? (
                      <img
                        src={getCurrentProfileImage()}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          // If image fails to load, hide it and show default icon
                          console.log('Failed to load image:', getCurrentProfileImage());
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                    ) : null}
                    {!getCurrentProfileImage() && (
                      <div className="w-full h-full flex items-center justify-center">
                        <FiUser className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                    {/* Fallback div for when image fails to load */}
                    <div className="w-full h-full flex items-center justify-center" style={{ display: 'none' }}>
                      <FiUser className="w-8 h-8 text-gray-400" />
                    </div>
                  </div>
                  <label className={`absolute bottom-0 right-0 p-1.5 rounded-full cursor-pointer transition-colors ${
                    isUploadingImage
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white`}>
                    {isUploadingImage ? (
                      <FiLoader className="w-3 h-3 animate-spin" />
                    ) : (
                      <FiCamera className="w-3 h-3" />
                    )}
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      disabled={isUploadingImage}
                      className="hidden"
                    />
                  </label>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">
                    Upload a professional photo to help students recognize you
                  </p>
                  <p className="text-xs text-gray-500 mb-2">
                    Requirements: Square image, at least 200x200px, maximum 20MB file size
                  </p>

                  {/* Error Message */}
                  {imageError && (
                    <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-start">
                        <FiAlertCircle className="w-4 h-4 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
                        <p className="text-sm text-red-700">{imageError}</p>
                      </div>
                    </div>
                  )}

                  {/* Upload Status Messages */}
                  {isUploadingImage && (
                    <div className="flex items-center text-blue-600 text-xs">
                      <FiLoader className="w-3 h-3 animate-spin mr-1" />
                      Uploading image...
                    </div>
                  )}

                  {imageUploadSuccess && (
                    <div className="flex items-center text-green-600 text-xs">
                      <FiCheck className="w-3 h-3 mr-1" />
                      Profile picture updated successfully!
                    </div>
                  )}

                  {userSlice.error && (
                    <div className="flex items-center text-red-600 text-xs">
                      <FiAlertCircle className="w-3 h-3 mr-1" />
                      {typeof userSlice.error === 'string' ? userSlice.error : 'Failed to upload image'}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bio *
                  </label>
                  <textarea
                    name="bio"
                    value={profile?.bio || ''}
                    onChange={handleInputChange}
                    required
                    rows={4}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      validationErrors.bio ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Tell students about yourself, your background, and teaching philosophy..."
                  />
                  {validationErrors.bio && (
                    <p className="mt-1 text-sm text-red-600">{validationErrors.bio}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Experience Years *
                    </label>
                    <div className="relative">
                      <FiClock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="number"
                        name="experience_years"
                        value={profile?.experience_years || ''}
                        onChange={handleInputChange}
                        required
                        min="0"
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          validationErrors.experience_years ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {validationErrors.experience_years && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.experience_years}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hourly Rate (USD) *
                    </label>
                    <div className="relative">
                      <FiDollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="number"
                        name="hourly_rate"
                        value={profile?.hourly_rate || ''}
                        onChange={handleInputChange}
                        required
                        min="1"
                        step="0.01"
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          validationErrors.hourly_rate ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {validationErrors.hourly_rate && (
                      <p className="mt-1 text-sm text-red-600">{validationErrors.hourly_rate}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Expertise Tab */}
        {activeTab === 'expertise' && (
          <div className="space-y-6">
            {/* Expertise Subjects */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Expertise Subjects</h3>
              <p className="text-sm text-gray-600 mb-4">
                Select the subjects you have expertise in and can mentor students effectively
              </p>

              <SubjectSelector
                selectedSubjectIds={profile?.expertise_subject_ids || []}
                onSelectionChange={(subjectIds) => updateSubjectSelection('expertise_subject_ids', subjectIds)}
                label="Expertise Subjects *"
                placeholder="Search for subjects you're expert in..."
                className="w-full"
              />

              {validationErrors.expertise_subject_ids && (
                <p className="mt-2 text-sm text-red-600">{validationErrors.expertise_subject_ids}</p>
              )}
            </div>

            {/* Preferred Subjects */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Preferred Teaching Subjects</h3>
              <p className="text-sm text-gray-600 mb-4">
                Select the subjects you prefer to teach and would like to focus on
              </p>

              <SubjectSelector
                selectedSubjectIds={profile?.preferred_subject_ids || []}
                onSelectionChange={(subjectIds) => updateSubjectSelection('preferred_subject_ids', subjectIds)}
                label="Preferred Subjects *"
                placeholder="Search for subjects you prefer to teach..."
                className="w-full"
              />

              {validationErrors.preferred_subject_ids && (
                <p className="mt-2 text-sm text-red-600">{validationErrors.preferred_subject_ids}</p>
              )}
            </div>

            {/* Languages */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Languages</h3>
              <p className="text-sm text-gray-600 mb-4">
                Languages you can teach in
              </p>

              <div className="space-y-3">
                {(profile?.languages || ['English']).map((language, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <div className="flex-1">
                      <input
                        type="text"
                        value={language}
                        onChange={(e) => handleArrayFieldChange('languages', index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., English, Spanish, French..."
                      />
                    </div>
                    {(profile?.languages || []).length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeArrayField('languages', index)}
                        className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                      >
                        <FiX className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                ))}

                <button
                  type="button"
                  onClick={() => addArrayField('languages')}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
                >
                  <FiPlus className="h-4 w-4 mr-2" />
                  Add Language
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Availability Tab */}
        {activeTab === 'availability' && (
          <div className="space-y-6">
            {/* Availability Hours */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Availability Hours</h3>
              <p className="text-sm text-gray-600 mb-4">
                Set your available time slots for mentoring sessions
              </p>

              <div className="space-y-4">
                {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map(day => (
                  <div key={day} className="flex items-center space-x-4">
                    <div className="w-24">
                      <label className="block text-sm font-medium text-gray-700 capitalize">
                        {day}
                      </label>
                    </div>
                    <div className="flex-1">
                      <input
                        type="text"
                        value={profile?.availability_hours?.[day]?.join(', ') || ''}
                        onChange={(e) => {
                          const timeSlots = e.target.value.split(',').map(slot => slot.trim()).filter(Boolean);
                          updateLocalProfile({
                            availability_hours: {
                              ...profile?.availability_hours,
                              [day]: timeSlots
                            }
                          });
                        }}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="e.g., 09:00-12:00, 14:00-17:00"
                      />
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>Format:</strong> Use 24-hour format (e.g., 09:00-12:00, 14:00-17:00).
                  Separate multiple time slots with commas.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Preferences Tab */}
        {activeTab === 'preferences' && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <div className="text-center">
              <FiFileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
              <p className="text-gray-600">
                Additional preferences and settings will be available soon.
              </p>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default MentorSettings;
