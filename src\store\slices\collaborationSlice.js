import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import axios from 'axios';
import API_BASE_URL from '../../utils/api/API_URL';

const BASE_URL = `${API_BASE_URL}/api/institute/mentors`;
const getAuthToken = () => localStorage.getItem("token");

// Async thunks for API calls
export const fetchSentInvitations = createAsyncThunk(
  'collaboration/fetchSentInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/invites/sent`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const fetchReceivedInvitations = createAsyncThunk(
  'collaboration/fetchReceivedInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const res = await axios.get(`${BASE_URL}/invites/received`, {
        params: { page, size },
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const sendInvitation = createAsyncThunk(
  'collaboration/sendInvitation',
  async (invitationData, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/invite`, invitationData, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

export const respondToInvitation = createAsyncThunk(
  'collaboration/respondToInvitation',
  async ({ invitationId, response }, thunkAPI) => {
    try {
      const res = await axios.post(`${BASE_URL}/invite/${invitationId}/respond`, response, {
        headers: { Authorization: `Bearer ${getAuthToken()}` }
      });
      return res.data;
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

// Fetch all invitations (both sent and received)
export const fetchAllInvitations = createAsyncThunk(
  'collaboration/fetchAllInvitations',
  async ({ page = 1, size = 20 }, thunkAPI) => {
    try {
      const [sentRes, receivedRes] = await Promise.all([
        axios.get(`${BASE_URL}/invites/sent`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        }),
        axios.get(`${BASE_URL}/invites/received`, {
          params: { page, size },
          headers: { Authorization: `Bearer ${getAuthToken()}` }
        })
      ]);

      return {
        sent: sentRes.data,
        received: receivedRes.data
      };
    } catch (err) {
      return thunkAPI.rejectWithValue(err.response?.data || err.message);
    }
  }
);

const initialState = {
  sentInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  receivedInvitations: {
    data: [],
    total: 0,
    page: 1,
    size: 20,
    hasNext: false,
    hasPrev: false,
    loading: false,
    error: null
  },
  sendInvitation: {
    loading: false,
    error: null,
    success: false
  },
  respondInvitation: {
    loading: false,
    error: null,
    success: false
  }
};

const collaborationSlice = createSlice({
  name: 'collaboration',
  initialState,
  reducers: {
    clearSendInvitationState: (state) => {
      state.sendInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearRespondInvitationState: (state) => {
      state.respondInvitation = {
        loading: false,
        error: null,
        success: false
      };
    },
    clearErrors: (state) => {
      state.sentInvitations.error = null;
      state.receivedInvitations.error = null;
      state.sendInvitation.error = null;
      state.respondInvitation.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch sent invitations
    builder
      .addCase(fetchSentInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.sentInvitations.error = null;
      })
      .addCase(fetchSentInvitations.fulfilled, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.data = action.payload.invitations || [];
        state.sentInvitations.total = action.payload.total || 0;
        state.sentInvitations.page = action.payload.page || 1;
        state.sentInvitations.size = action.payload.size || 20;
        state.sentInvitations.hasNext = action.payload.has_next || false;
        state.sentInvitations.hasPrev = action.payload.has_prev || false;
      })
      .addCase(fetchSentInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.sentInvitations.error = action.payload;
      });

    // Fetch received invitations
    builder
      .addCase(fetchReceivedInvitations.pending, (state) => {
        state.receivedInvitations.loading = true;
        state.receivedInvitations.error = null;
      })
      .addCase(fetchReceivedInvitations.fulfilled, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.data = action.payload.invitations || [];
        state.receivedInvitations.total = action.payload.total || 0;
        state.receivedInvitations.page = action.payload.page || 1;
        state.receivedInvitations.size = action.payload.size || 20;
        state.receivedInvitations.hasNext = action.payload.has_next || false;
        state.receivedInvitations.hasPrev = action.payload.has_prev || false;
      })
      .addCase(fetchReceivedInvitations.rejected, (state, action) => {
        state.receivedInvitations.loading = false;
        state.receivedInvitations.error = action.payload;
      });

    // Send invitation
    builder
      .addCase(sendInvitation.pending, (state) => {
        state.sendInvitation.loading = true;
        state.sendInvitation.error = null;
        state.sendInvitation.success = false;
      })
      .addCase(sendInvitation.fulfilled, (state) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.success = true;
      })
      .addCase(sendInvitation.rejected, (state, action) => {
        state.sendInvitation.loading = false;
        state.sendInvitation.error = action.payload;
      });

    // Respond to invitation
    builder
      .addCase(respondToInvitation.pending, (state) => {
        state.respondInvitation.loading = true;
        state.respondInvitation.error = null;
        state.respondInvitation.success = false;
      })
      .addCase(respondToInvitation.fulfilled, (state) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.success = true;
      })
      .addCase(respondToInvitation.rejected, (state, action) => {
        state.respondInvitation.loading = false;
        state.respondInvitation.error = action.payload;
      });

    // Fetch all invitations
    builder
      .addCase(fetchAllInvitations.pending, (state) => {
        state.sentInvitations.loading = true;
        state.receivedInvitations.loading = true;
      })
      .addCase(fetchAllInvitations.fulfilled, (state, action) => {
        // Update sent invitations
        state.sentInvitations.loading = false;
        state.sentInvitations.data = action.payload.sent?.invitations || [];
        state.sentInvitations.total = action.payload.sent?.total || 0;
        
        // Update received invitations
        state.receivedInvitations.loading = false;
        state.receivedInvitations.data = action.payload.received?.invitations || [];
        state.receivedInvitations.total = action.payload.received?.total || 0;
      })
      .addCase(fetchAllInvitations.rejected, (state, action) => {
        state.sentInvitations.loading = false;
        state.receivedInvitations.loading = false;
        state.sentInvitations.error = action.payload;
        state.receivedInvitations.error = action.payload;
      });
  }
});

export const { clearSendInvitationState, clearRespondInvitationState, clearErrors } = collaborationSlice.actions;

// Selectors
export const selectSentInvitations = (state) => state.collaboration.sentInvitations;
export const selectReceivedInvitations = (state) => state.collaboration.receivedInvitations;
export const selectSendInvitationState = (state) => state.collaboration.sendInvitation;
export const selectRespondInvitationState = (state) => state.collaboration.respondInvitation;

export default collaborationSlice.reducer;
