/* Typography */
.h1 {
    @apply text-4xl font-extrabold tracking-tighter;
}

.h2 {
    @apply text-3xl font-extrabold tracking-tighter;
}

.h3 {
    @apply text-3xl font-extrabold;
}

.h4 {
    @apply text-2xl font-extrabold tracking-tight;
}

@media (width >= theme(--breakpoint-md)) {
    .h1 {
        @apply text-5xl;
    }

    .h2 {
        @apply text-4xl;
    }
}

/* Buttons */
.btn,
.btn-lg,
.btn-sm,
.btn-xs {
    @apply font-medium text-sm inline-flex items-center justify-center border border-transparent rounded-lg leading-5 shadow-xs transition;
}

.btn {
    @apply px-3 py-2;
}

.btn-lg {
    @apply px-4 py-3;
}

.btn-sm {
    @apply px-2 py-1;
}

.btn-xs {
    @apply px-2 py-0.5;
}

/* Forms */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

.form-input,
.form-textarea,
.form-multiselect,
.form-select,
.form-checkbox,
.form-radio {
    @apply bg-white dark:bg-gray-900/30 border focus:ring-0 focus:ring-offset-0 dark:disabled:bg-gray-700/30 dark:disabled:border-gray-700 dark:disabled:hover:border-gray-700;
}

.form-checkbox {
    @apply rounded-sm;
}

.form-input,
.form-textarea,
.form-multiselect,
.form-select {
    @apply text-sm text-gray-800 dark:text-gray-100 leading-5 py-2 px-3 border-gray-200 hover:border-gray-300 focus:border-gray-300 dark:border-gray-700/60 dark:hover:border-gray-600 dark:focus:border-gray-600 shadow-xs rounded-lg;
}

.form-input,
.form-textarea {
    @apply placeholder-gray-400 dark:placeholder-gray-500;
}

.form-select {
    @apply pr-10;
}

.form-checkbox,
.form-radio {
    @apply text-violet-500 checked:bg-violet-500 checked:border-transparent border border-gray-300 dark:border-gray-700/60 dark:checked:border-transparent focus-visible:ring-2 focus-visible:ring-violet-500/50;
}

/* Switch element */
.form-switch {
    @apply relative select-none;
    width: 44px;
}

.form-switch label {
    @apply block overflow-hidden cursor-pointer h-6 rounded-full;
}

.form-switch label > span:first-child {
    @apply absolute block rounded-full;
    width: 20px;
    height: 20px;
    top: 2px;
    left: 2px;
    right: 50%;
    transition: all .15s ease-out;
}

.form-switch input[type="checkbox"] + label {
  @apply bg-gray-400 dark:bg-gray-700;
}

.form-switch input[type="checkbox"]:checked + label {
  @apply bg-violet-500;
}

.form-switch input[type="checkbox"]:checked + label > span:first-child {
    left: 22px;
}

.form-switch input[type="checkbox"]:disabled + label {
    @apply cursor-not-allowed bg-gray-100 dark:bg-gray-700/20 border border-gray-200 dark:border-gray-700/60;
}

.form-switch input[type="checkbox"]:disabled + label > span:first-child {
    @apply bg-gray-400 dark:bg-gray-600;
}

/* Chrome, Safari and Opera */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Authentication Page Styles */
.auth-container {
  @apply min-h-screen bg-gradient-to-br from-violet-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-gray-800;
}

.auth-card {
  @apply bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-100 dark:border-gray-700 p-8 w-full max-w-md;
}

.auth-header {
  @apply text-center mb-8;
}

.auth-title {
  @apply text-3xl font-bold text-gray-900 dark:text-white mb-2;
}

.auth-subtitle {
  @apply text-gray-600 dark:text-gray-400 text-sm;
}

.auth-form-field {
  @apply space-y-2 mb-4;
}

.auth-input {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg
         focus:ring-2 focus:ring-violet-500 focus:border-transparent
         bg-white dark:bg-gray-700 text-gray-900 dark:text-white
         placeholder-gray-500 dark:placeholder-gray-400
         transition-all duration-200 text-base;
}

.auth-input:focus {
  @apply outline-none ring-2 ring-violet-500 border-transparent;
}

.auth-input.error {
  @apply border-red-500 dark:border-red-400 ring-2 ring-red-500;
}

.auth-button {
  @apply w-full bg-violet-600 hover:bg-violet-700 focus:bg-violet-700
         disabled:bg-violet-300 dark:disabled:bg-violet-800
         text-white font-medium py-3 px-4 rounded-lg
         transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2
         touch-manipulation min-h-[44px];
}

.auth-error {
  @apply text-red-600 dark:text-red-400 text-sm flex items-center gap-1;
}

.auth-link {
  @apply text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300
         font-medium transition-colors duration-200;
}

/* Role Selection Cards */
.role-card {
  @apply border-2 rounded-xl p-6 cursor-pointer text-center transition-all duration-200
         bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700
         hover:border-violet-300 dark:hover:border-violet-600 hover:shadow-lg
         focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2;
}

.role-card.selected {
  @apply border-violet-500 dark:border-violet-400 bg-violet-50 dark:bg-violet-900/20 shadow-lg;
}

.role-card-title {
  @apply text-xl font-semibold text-gray-900 dark:text-white mb-2;
}

.role-card-description {
  @apply text-gray-600 dark:text-gray-400 text-sm;
}

/* Mobile Optimizations for Auth */
@media (max-width: 640px) {
  .auth-card {
    @apply p-6 mx-4 rounded-xl;
  }

  .auth-title {
    @apply text-2xl;
  }

  .role-card {
    @apply p-4;
  }

  .role-card-title {
    @apply text-lg;
  }
}