import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { FiUser, FiMail, FiCreditCard } from 'react-icons/fi';
import { signupUser } from '../../store/slices/SignupSlice';
import { FormField, TextInput, PasswordInput } from '../ui/FormComponents';
import CountrySelector from '../ui/CountrySelector';

const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    cnic: '',
    passport: '',
    password: '',
    confirmPassword: '',
  });

  const [errors, setErrors] = useState({});
  const [countryCode, setCountryCode] = useState('PK');

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleIdentityChange = (e) => {
    const value = e.target.value;
    if (countryCode === 'PK') {
      setFormData(prev => ({ ...prev, cnic: value, passport: '' }));
    } else {
      setFormData(prev => ({ ...prev, passport: value, cnic: '' }));
    }
    
    if (errors.cnic || errors.passport) {
      setErrors(prev => ({ ...prev, cnic: '', passport: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    if (!formData.password) newErrors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (countryCode === 'PK') {
      if (!formData.cnic && !formData.passport) {
        newErrors.cnic = 'CNIC or Passport is required';
      }
    } else {
      if (!formData.passport) {
        newErrors.passport = 'Passport number is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    const submitData = {
      ...formData,
      role,
      countryCode,
    };

    try {
      await dispatch(signupUser(submitData)).unwrap();
      navigate('/email-verification');
    } catch (err) {
      console.error('Signup failed:', err);
    }
  };

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3">
            Create your account
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-base">
            Join EduFair as a {role} and start your journey today.
          </p>
        </div>

        <div className="space-y-6">
          {/* Row 1: Full Name and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Full Name" error={errors.fullName}>
              <TextInput
                name="fullName"
                placeholder="Enter your full name"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
                className="text-base"
              />
            </FormField>

            <FormField label="Email Address" error={errors.email}>
              <TextInput
                type="email"
                name="email"
                placeholder="Enter your email address"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
                className="text-base"
              />
            </FormField>
          </div>

          {/* Row 2: Phone Number (Full Width) */}
          <FormField label="Phone Number" error={errors.phone}>
            <div className="relative">
              {/* Country Selector positioned on top of phone input */}
              <div className="absolute left-0 top-0 z-10">
                <CountrySelector
                  value={countryCode}
                  onChange={(newCountryCode) => {
                    setCountryCode(newCountryCode);
                  }}
                  error={!!errors.phone}
                  compact={true}
                />
              </div>

              {/* Phone Number Input */}
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, phone: e.target.value }));
                  if (errors.phone) {
                    setErrors(prev => ({ ...prev, phone: '' }));
                  }
                }}
                placeholder="Enter your phone number"
                className={`
                  w-full pl-20 pr-4 py-3 border-2 rounded-xl transition-all duration-200 text-base min-h-[48px]
                  bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                  placeholder-gray-500 dark:placeholder-gray-400
                  focus:outline-none focus:ring-2 focus:ring-violet-500/50
                  ${errors.phone
                    ? 'border-red-400 dark:border-red-500 focus:border-red-500'
                    : 'border-gray-300 dark:border-gray-600 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500'
                  }
                `}
              />
            </div>
          </FormField>

          {/* Row 3: CNIC/Passport (Full Width) */}
          <FormField 
            label={countryCode === 'PK' ? 'CNIC or Passport' : 'Passport Number'} 
            error={errors.cnic || errors.passport}
          >
            <TextInput
              placeholder={countryCode === 'PK' ? 'Enter your CNIC or Passport Number' : 'Enter your Passport Number'}
              value={countryCode === 'PK' ? formData.cnic || formData.passport : formData.passport}
              onChange={handleIdentityChange}
              icon={FiCreditCard}
              error={errors.cnic || errors.passport}
              className="text-base"
            />
          </FormField>

          {/* Row 4: Password and Confirm Password */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
                className="text-base"
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
                className="text-base"
              />
            </FormField>
          </div>
        </div>

        <div className="space-y-6 mt-8">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-center text-red-700 dark:text-red-400 text-sm">
                <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-violet-600 hover:bg-violet-700 active:bg-violet-800 disabled:bg-violet-300 dark:disabled:bg-violet-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-violet-500/50 transform hover:scale-[1.02] disabled:transform-none disabled:cursor-not-allowed min-h-[56px] text-base"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating account...
              </div>
            ) : (
              'Create account'
            )}
          </button>

          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => navigate('/Login')}
                className="text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 font-semibold transition-colors duration-200"
              >
                Sign in
              </button>
            </p>
          </div>
        </div>
      </form>
    </div>
  );
};

export default SignupForm;
