import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { FiUser, FiMail, FiCreditCard } from 'react-icons/fi';
import { signupUser } from '../../store/slices/SignupSlice';
import { FormField, TextInput, PasswordInput } from '../ui/FormComponents';
import { Card, Stack } from '../ui/layout';

const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    cnic: '',
    passport: '',
    password: '',
    confirmPassword: ''
  });
  const [countryCode, setCountryCode] = useState('pk');
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleIdentityChange = (e) => {
    const value = e.target.value;
    if (countryCode === 'pk') {
      setFormData(prev => ({ ...prev, cnic: value, passport: '' }));
    } else {
      setFormData(prev => ({ ...prev, passport: value, cnic: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    if (!formData.password) newErrors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (countryCode === 'pk' && !formData.cnic) {
      newErrors.cnic = 'CNIC is required';
    } else if (countryCode !== 'pk' && !formData.passport) {
      newErrors.passport = 'Passport number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    const userData = {
      username: formData.fullName,
      email: formData.email,
      mobile: formData.phone,
      password: formData.password,
      user_type: role,
      country: countryCode.toUpperCase(),
      ...(countryCode === 'pk' ? { cnic: formData.cnic } : { passport: formData.passport })
    };

    dispatch(signupUser(userData));
  };

  return (
    <Card className="w-full">
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3">
            Create your account
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-base">
            Join EduFair as a {role} and start your journey today.
          </p>
        </div>

        <div className="space-y-6">
          {/* Row 1: Full Name and Email */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Full Name" error={errors.fullName}>
              <TextInput
                name="fullName"
                placeholder="Enter your full name"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
                className="text-base"
              />
            </FormField>

            <FormField label="Email Address" error={errors.email}>
              <TextInput
                type="email"
                name="email"
                placeholder="Enter your email address"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
                className="text-base"
              />
            </FormField>
          </div>

          {/* Row 2: Phone Number (Full Width) */}
          <FormField label="Phone Number" error={errors.phone}>
            <div className="relative">
              <select
                value={countryCode}
                onChange={(e) => setCountryCode(e.target.value)}
                className="absolute left-0 top-0 h-12 w-24 border-2 border-r-0 border-gray-300 dark:border-gray-600 rounded-l-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-xs focus:outline-none focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200 z-10 cursor-pointer"
                style={{ fontSize: '11px', paddingLeft: '4px', paddingRight: '2px' }}
              >
                <option value="af">🇦🇫 +93</option>
                <option value="al">🇦🇱 +355</option>
                <option value="dz">🇩🇿 +213</option>
                <option value="ad">🇦🇩 +376</option>
                <option value="ao">🇦🇴 +244</option>
                <option value="ar">🇦🇷 +54</option>
                <option value="am">🇦🇲 +374</option>
                <option value="au">🇦🇺 +61</option>
                <option value="at">🇦🇹 +43</option>
                <option value="az">🇦🇿 +994</option>
                <option value="bh">🇧🇭 +973</option>
                <option value="bd">🇧🇩 +880</option>
                <option value="by">🇧🇾 +375</option>
                <option value="be">🇧🇪 +32</option>
                <option value="bz">🇧🇿 +501</option>
                <option value="bj">🇧🇯 +229</option>
                <option value="bt">🇧🇹 +975</option>
                <option value="bo">🇧🇴 +591</option>
                <option value="ba">🇧🇦 +387</option>
                <option value="bw">🇧🇼 +267</option>
                <option value="br">🇧🇷 +55</option>
                <option value="bn">🇧🇳 +673</option>
                <option value="bg">🇧🇬 +359</option>
                <option value="bf">🇧🇫 +226</option>
                <option value="bi">🇧🇮 +257</option>
                <option value="kh">�🇭 +855</option>
                <option value="cm">🇨🇲 +237</option>
                <option value="ca">🇨🇦 +1</option>
                <option value="cv">🇨🇻 +238</option>
                <option value="cf">🇨🇫 +236</option>
                <option value="td">🇹🇩 +235</option>
                <option value="cl">🇨🇱 +56</option>
                <option value="cn">🇨🇳 +86</option>
                <option value="co">🇨🇴 +57</option>
                <option value="km">🇰🇲 +269</option>
                <option value="cg">🇨🇬 +242</option>
                <option value="cd">🇨🇩 +243</option>
                <option value="cr">🇨🇷 +506</option>
                <option value="ci">🇨🇮 +225</option>
                <option value="hr">🇭🇷 +385</option>
                <option value="cu">🇨🇺 +53</option>
                <option value="cy">🇨🇾 +357</option>
                <option value="cz">🇨🇿 +420</option>
                <option value="dk">🇩🇰 +45</option>
                <option value="dj">🇩🇯 +253</option>
                <option value="dm">🇩🇲 +1767</option>
                <option value="do">🇩🇴 +1809</option>
                <option value="ec">🇪🇨 +593</option>
                <option value="eg">🇪🇬 +20</option>
                <option value="sv">🇸🇻 +503</option>
                <option value="gq">🇬🇶 +240</option>
                <option value="er">🇪🇷 +291</option>
                <option value="ee">🇪🇪 +372</option>
                <option value="et">🇪🇹 +251</option>
                <option value="fj">🇫🇯 +679</option>
                <option value="fi">🇫🇮 +358</option>
                <option value="fr">🇫🇷 +33</option>
                <option value="ga">🇬🇦 +241</option>
                <option value="gm">🇬🇲 +220</option>
                <option value="ge">🇬🇪 +995</option>
                <option value="de">🇩🇪 +49</option>
                <option value="gh">🇬🇭 +233</option>
                <option value="gr">🇬🇷 +30</option>
                <option value="gd">🇬🇩 +1473</option>
                <option value="gt">🇬🇹 +502</option>
                <option value="gn">🇬🇳 +224</option>
                <option value="gw">🇬🇼 +245</option>
                <option value="gy">🇬🇾 +592</option>
                <option value="ht">🇭🇹 +509</option>
                <option value="hn">🇭� +504</option>
                <option value="hu">🇭🇺 +36</option>
                <option value="is">🇮🇸 +354</option>
                <option value="in">🇮🇳 +91</option>
                <option value="id">🇮🇩 +62</option>
                <option value="ir">🇮🇷 +98</option>
                <option value="iq">🇮🇶 +964</option>
                <option value="ie">🇮� +353</option>
                <option value="it">🇮🇹 +39</option>
                <option value="jm">🇯🇲 +1876</option>
                <option value="jp">🇯🇵 +81</option>
                <option value="jo">🇯🇴 +962</option>
                <option value="kz">🇰🇿 +7</option>
                <option value="ke">🇰🇪 +254</option>
                <option value="ki">🇰🇮 +686</option>
                <option value="kp">🇰🇵 +850</option>
                <option value="kr">🇰🇷 +82</option>
                <option value="kw">🇰🇼 +965</option>
                <option value="kg">🇰🇬 +996</option>
                <option value="la">🇱�🇦 +856</option>
                <option value="lv">🇱🇻 +371</option>
                <option value="lb">�🇧 +961</option>
                <option value="ls">🇱🇸 +266</option>
                <option value="lr">🇱🇷 +231</option>
                <option value="ly">🇱🇾 +218</option>
                <option value="li">🇱🇮 +423</option>
                <option value="lt">🇱🇹 +370</option>
                <option value="lu">🇱🇺 +352</option>
                <option value="mk">🇲🇰 +389</option>
                <option value="mg">🇲🇬 +261</option>
                <option value="mw">🇲🇼 +265</option>
                <option value="my">🇲🇾 +60</option>
                <option value="mv">🇲🇻 +960</option>
                <option value="ml">🇲🇱 +223</option>
                <option value="mt">🇲🇹 +356</option>
                <option value="mh">🇲🇭 +692</option>
                <option value="mr">🇲🇷 +222</option>
                <option value="mu">🇲🇺 +230</option>
                <option value="mx">🇲🇽 +52</option>
                <option value="fm">🇫🇲 +691</option>
                <option value="md">🇲🇩 +373</option>
                <option value="mc">🇲🇨 +377</option>
                <option value="mn">🇲🇳 +976</option>
                <option value="me">🇲�🇪 +382</option>
                <option value="ma">🇲🇦 +212</option>
                <option value="mz">🇲🇿 +258</option>
                <option value="mm">🇲🇲 +95</option>
                <option value="na">🇳🇦 +264</option>
                <option value="nr">🇳🇷 +674</option>
                <option value="np">🇳🇵 +977</option>
                <option value="nl">🇳🇱 +31</option>
                <option value="nz">🇳🇿 +64</option>
                <option value="ni">🇳🇮 +505</option>
                <option value="ne">🇳🇪 +227</option>
                <option value="ng">🇳🇬 +234</option>
                <option value="no">🇳🇴 +47</option>
                <option value="om">🇴🇲 +968</option>
                <option value="pk" selected>🇵🇰 +92</option>
                <option value="pw">🇵🇼 +680</option>
                <option value="ps">🇵🇸 +970</option>
                <option value="pa">🇵🇦 +507</option>
                <option value="pg">🇵🇬 +675</option>
                <option value="py">🇵🇾 +595</option>
                <option value="pe">🇵🇪 +51</option>
                <option value="ph">🇵🇭 +63</option>
                <option value="pl">🇵🇱 +48</option>
                <option value="pt">🇵🇹 +351</option>
                <option value="qa">🇶🇦 +974</option>
                <option value="ro">🇷🇴 +40</option>
                <option value="ru">🇷🇺 +7</option>
                <option value="rw">🇷🇼 +250</option>
                <option value="kn">🇰🇳 +1869</option>
                <option value="lc">🇱🇨 +1758</option>
                <option value="vc">🇻🇨 +1784</option>
                <option value="ws">🇼� +685</option>
                <option value="sm">🇸🇲 +378</option>
                <option value="st">🇸🇹 +239</option>
                <option value="sa">🇸�🇦 +966</option>
                <option value="sn">🇸🇳 +221</option>
                <option value="rs">🇷🇸 +381</option>
                <option value="sc">�🇨 +248</option>
                <option value="sl">🇸🇱 +232</option>
                <option value="sg">🇸🇬 +65</option>
                <option value="sk">🇸🇰 +421</option>
                <option value="si">🇸🇮 +386</option>
                <option value="sb">🇸🇧 +677</option>
                <option value="so">🇸🇴 +252</option>
                <option value="za">🇿�🇦 +27</option>
                <option value="ss">🇸🇸 +211</option>
                <option value="es">🇪🇸 +34</option>
                <option value="lk">🇱🇰 +94</option>
                <option value="sd">🇸🇩 +249</option>
                <option value="sr">🇸🇷 +597</option>
                <option value="sz">🇸🇿 +268</option>
                <option value="se">🇸🇪 +46</option>
                <option value="ch">🇨🇭 +41</option>
                <option value="sy">🇸🇾 +963</option>
                <option value="tw">🇹🇼 +886</option>
                <option value="tj">🇹� +992</option>
                <option value="tz">🇹🇿 +255</option>
                <option value="th">🇹🇭 +66</option>
                <option value="tl">🇹🇱 +670</option>
                <option value="tg">🇹🇬 +228</option>
                <option value="to">🇹🇴 +676</option>
                <option value="tt">🇹🇹 +1868</option>
                <option value="tn">🇹🇳 +216</option>
                <option value="tr">🇹🇷 +90</option>
                <option value="tm">🇹🇲 +993</option>
                <option value="tv">🇹🇻 +688</option>
                <option value="ug">�🇺🇬 +256</option>
                <option value="ua">🇺🇦 +380</option>
                <option value="ae">🇦🇪 +971</option>
                <option value="gb">🇬🇧 +44</option>
                <option value="us">🇺🇸 +1</option>
                <option value="uy">🇺🇾 +598</option>
                <option value="uz">🇺🇿 +998</option>
                <option value="vu">🇻🇺 +678</option>
                <option value="va">🇻🇦 +39</option>
                <option value="ve">🇻🇪 +58</option>
                <option value="vn">🇻🇳 +84</option>
                <option value="ye">🇾🇪 +967</option>
                <option value="zm">🇿🇲 +260</option>
                <option value="zw">🇿🇼 +263</option>
              </select>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={(e) => {
                  setFormData(prev => ({ ...prev, phone: e.target.value }));
                  if (errors.phone) {
                    setErrors(prev => ({ ...prev, phone: '' }));
                  }
                }}
                placeholder="Enter your phone number"
                className="w-full pl-24 pr-4 py-3 border-2 border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-violet-500/50 focus:border-violet-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 text-base min-h-[48px] hover:border-gray-400 dark:hover:border-gray-500"
              />
            </div>
          </FormField>

          {/* Row 3: CNIC/Passport (Full Width) */}
          <FormField
            label={countryCode === 'pk' ? 'CNIC or Passport' : 'Passport Number'}
            error={errors.cnic || errors.passport}
          >
            <TextInput
              placeholder={countryCode === 'pk' ? 'Enter your CNIC or Passport Number' : 'Enter your Passport Number'}
              value={countryCode === 'pk' ? formData.cnic || formData.passport : formData.passport}
              onChange={handleIdentityChange}
              icon={FiCreditCard}
              error={errors.cnic || errors.passport}
              className="text-base"
            />
          </FormField>

          {/* Row 4: Password and Confirm Password */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Create a strong password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
                className="text-base"
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm your password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
                className="text-base"
              />
            </FormField>
          </div>
        </div>

        <div className="space-y-6 mt-8">
          {error && (
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
              <div className="flex items-center text-red-700 dark:text-red-400 text-sm">
                <svg className="w-4 h-4 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-violet-600 hover:bg-violet-700 active:bg-violet-800 disabled:bg-violet-300 dark:disabled:bg-violet-800 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-4 focus:ring-violet-500/50 transform hover:scale-[1.02] disabled:transform-none disabled:cursor-not-allowed min-h-[56px] text-base"
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Creating account...
              </div>
            ) : (
              'Create account'
            )}
          </button>

          <div className="text-center">
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              Already have an account?{' '}
              <button
                type="button"
                onClick={() => navigate('/Login')}
                className="text-violet-600 dark:text-violet-400 hover:text-violet-700 dark:hover:text-violet-300 font-semibold transition-colors duration-200"
              >
                Sign in
              </button>
            </p>
          </div>
        </div>


      </form>
    </Card>
  );
};

export default SignupForm;
