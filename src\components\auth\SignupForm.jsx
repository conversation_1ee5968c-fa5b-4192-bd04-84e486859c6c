import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PhoneInput from 'react-phone-input-2';
import { FiUser, FiMail, FiLock, FiCreditCard, FiEye, FiEyeOff } from 'react-icons/fi';
import { signupUser } from '../../store/slices/SignupSlice';
import { Card, Stack } from '../ui/layout';
import { FormField, TextInput, PasswordInput } from '../ui/FormComponents';

const SignupForm = ({ role }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { loading, error } = useSelector((state) => state.signup);

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    cnic: '',
    passport: '',
    password: '',
    confirmPassword: ''
  });
  const [countryCode, setCountryCode] = useState('pk');
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleIdentityChange = (e) => {
    const value = e.target.value;
    if (countryCode === 'pk') {
      setFormData(prev => ({ ...prev, cnic: value, passport: '' }));
    } else {
      setFormData(prev => ({ ...prev, passport: value, cnic: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.fullName.trim()) newErrors.fullName = 'Full name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    if (!formData.password) newErrors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (countryCode === 'pk' && !formData.cnic) {
      newErrors.cnic = 'CNIC is required';
    } else if (countryCode !== 'pk' && !formData.passport) {
      newErrors.passport = 'Passport number is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    const userData = {
      username: formData.fullName,
      email: formData.email,
      mobile: formData.phone,
      password: formData.password,
      user_type: role,
      country: countryCode.toUpperCase(),
      ...(countryCode === 'pk' ? { cnic: formData.cnic } : { passport: formData.passport })
    };

    dispatch(signupUser(userData));
  };

  return (
    <Card className="max-w-md w-full">
      <form onSubmit={handleSubmit}>
        <Stack gap="lg">
          <div>
            <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
              Start your free trial
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              No credit card required. Sign up with your work email.
            </p>
          </div>

          <Stack gap="md">
            <FormField label="Full Name" error={errors.fullName}>
              <TextInput
                name="fullName"
                placeholder="Full name"
                value={formData.fullName}
                onChange={handleChange}
                icon={FiUser}
                error={errors.fullName}
              />
            </FormField>

            <FormField label="Email" error={errors.email}>
              <TextInput
                type="email"
                name="email"
                placeholder="Email address"
                value={formData.email}
                onChange={handleChange}
                icon={FiMail}
                error={errors.email}
              />
            </FormField>

            <FormField label="Phone" error={errors.phone}>
              <PhoneInput
                country="pk"
                value={formData.phone}
                onChange={(phone, country) => {
                  setFormData(prev => ({ ...prev, phone }));
                  setCountryCode(country.countryCode);
                }}
                inputProps={{
                  name: 'phone',
                  className: "w-full pl-14 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-violet-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                }}
              />
            </FormField>

            <FormField 
              label={countryCode === 'pk' ? 'CNIC or Passport' : 'Passport Number'} 
              error={errors.cnic || errors.passport}
            >
              <TextInput
                placeholder={countryCode === 'pk' ? 'CNIC or Passport Number' : 'Passport Number'}
                value={countryCode === 'pk' ? formData.cnic || formData.passport : formData.passport}
                onChange={handleIdentityChange}
                icon={FiCreditCard}
                error={errors.cnic || errors.passport}
              />
            </FormField>

            <FormField label="Password" error={errors.password}>
              <PasswordInput
                name="password"
                placeholder="Password"
                value={formData.password}
                onChange={handleChange}
                error={errors.password}
              />
            </FormField>

            <FormField label="Confirm Password" error={errors.confirmPassword}>
              <PasswordInput
                name="confirmPassword"
                placeholder="Confirm password"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
              />
            </FormField>
          </Stack>

          {error && (
            <div className="text-red-500 dark:text-red-400 text-sm text-center">
              {error}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-violet-600 hover:bg-violet-700 disabled:bg-violet-300 text-white py-3 rounded-md transition-colors"
          >
            {loading ? 'Creating account...' : 'Create account'}
          </button>

          <p className="text-center text-sm text-gray-600 dark:text-gray-400">
            Already have an account?{' '}
            <button
              type="button"
              onClick={() => navigate('/Login')}
              className="text-violet-600 hover:text-violet-700 font-medium"
            >
              Sign in
            </button>
          </p>
        </Stack>
      </form>
    </Card>
  );
};

export default SignupForm;
